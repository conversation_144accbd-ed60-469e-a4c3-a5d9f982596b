import { Router } from 'express';
import { TaskController } from '../controllers/taskController';
import { protectRoute } from '../middlewares/authMiddleware';

const router = Router();

// All task routes require authentication
router.use(protectRoute);

// Task routes
router.post('/', TaskController.createTask);
router.get('/', TaskController.getTasks);
router.get('/today', TaskController.getTodayTasks);
router.get('/:id', TaskController.getTaskById);
router.put('/:id', TaskController.updateTask);
router.delete('/:id', TaskController.deleteTask);

export default router;
