{"name": "simplelife-backend", "version": "1.0.0", "description": "Backend API for SimpleLife task management application", "main": "dist/server.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "start": "node dist/server.js", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:studio": "npx prisma studio", "db:reset": "npx prisma migrate reset", "db:deploy": "npx prisma migrate deploy"}, "keywords": ["api", "express", "typescript", "prisma", "postgresql"], "author": "ZPCLLC", "license": "MIT", "dependencies": {"@prisma/client": "^6.8.2", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.8.10", "prisma": "^6.8.2", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2"}}