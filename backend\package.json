{"name": "focusflow-backend", "version": "1.0.0", "description": "Backend API for FocusFlow task management application", "main": "dist/server.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "start": "node dist/server.js", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:studio": "npx prisma studio", "db:reset": "npx prisma migrate reset", "db:deploy": "npx prisma migrate deploy"}, "keywords": ["api", "express", "typescript", "prisma", "postgresql"], "author": "FocusFlow Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "@prisma/client": "^5.6.0", "zod": "^3.22.4"}, "devDependencies": {"typescript": "^5.2.2", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "@types/express": "^4.17.21", "@types/node": "^20.8.10", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/cookie-parser": "^1.4.6", "@types/cors": "^2.8.17", "prisma": "^5.6.0"}}