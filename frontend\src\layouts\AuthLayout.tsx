import React from 'react';
import { Outlet } from 'react-router-dom';
import { Brain } from 'lucide-react';

interface AuthLayoutProps {
  children?: React.ReactNode;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Logo */}
        <div className="text-center">
          <div className="flex justify-center">
            <div className="flex items-center space-x-2">
              <Brain className="h-12 w-12 text-primary-600" />
              <span className="text-3xl font-bold text-secondary-900">SimpleLife</span>
            </div>
          </div>
          <p className="mt-2 text-sm text-secondary-600">
            Simplify your life, one task at a time
          </p>
        </div>

        {/* Auth Form */}
        <div className="card">
          <div className="card-content">
            {children || <Outlet />}
          </div>
        </div>

        {/* Footer */}
        <div className="text-center">
          <p className="text-xs text-secondary-500">
            Built with ❤️ for better focus and productivity
          </p>
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;
