# Database
DATABASE_URL="postgresql://username:password@localhost:5432/focusflow_db?schema=public"

# JWT Secret (generate a secure random string)
JWT_SECRET="your-super-secret-jwt-key-here"

# Server Configuration
PORT=3001
NODE_ENV=development

# Frontend URL (for CORS)
CLIENT_URL="http://localhost:5173"

# Optional: Database connection for development
# You can use a local PostgreSQL instance or a cloud service like:
# - Supabase: postgresql://postgres:[password]@[host]:5432/postgres
# - Neon: postgresql://[user]:[password]@[host]/[dbname]
# - Railway: postgresql://postgres:[password]@[host]:5432/railway
