import React, { useState, useRef, useEffect } from 'react';
import { Plus, Send, X } from 'lucide-react';
import { useInboxStore } from '@/store/inboxStore';
import Button from '@/components/ui/Button';
import { cn } from '@/utils/cn';

interface QuickAddProps {
  className?: string;
}

const QuickAdd: React.FC<QuickAddProps> = ({ className }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { addInboxItem } = useInboxStore();
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Focus textarea when opened
  useEffect(() => {
    if (isOpen && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [isOpen]);

  // <PERSON>le keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + Shift + A to open quick add
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'A') {
        event.preventDefault();
        setIsOpen(true);
      }
      
      // Escape to close
      if (event.key === 'Escape' && isOpen) {
        handleClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!content.trim() || isSubmitting) return;

    try {
      setIsSubmitting(true);
      await addInboxItem(content.trim());
      setContent('');
      setIsOpen(false);
    } catch (error) {
      console.error('Failed to add inbox item:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    setContent('');
  };

  const handleTextareaKeyDown = (e: React.KeyboardEvent) => {
    // Ctrl/Cmd + Enter to submit
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className={cn(
          'fixed bottom-6 right-6 z-40 flex h-14 w-14 items-center justify-center rounded-full bg-primary-600 text-white shadow-soft-lg transition-all hover:bg-primary-700 hover:scale-105 focus-ring',
          className
        )}
        title="Quick Add (Ctrl+Shift+A)"
      >
        <Plus className="h-6 w-6" />
      </button>
    );
  }

  return (
    <div className="fixed bottom-6 right-6 z-40 w-80">
      <div className="card animate-slide-up">
        <div className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-secondary-900">
              Quick Add
            </h3>
            <button
              onClick={handleClose}
              className="rounded-md p-1 text-secondary-400 hover:bg-secondary-100 hover:text-secondary-600 focus-ring"
            >
              <X className="h-4 w-4" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-3">
            <textarea
              ref={textareaRef}
              value={content}
              onChange={(e) => setContent(e.target.value)}
              onKeyDown={handleTextareaKeyDown}
              placeholder="What's on your mind? (Ctrl+Enter to save)"
              className="w-full resize-none rounded-lg border border-secondary-300 px-3 py-2 text-sm placeholder:text-secondary-500 focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              rows={3}
              maxLength={1000}
            />

            <div className="flex items-center justify-between">
              <span className="text-xs text-secondary-500">
                {content.length}/1000
              </span>
              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleClose}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  size="sm"
                  disabled={!content.trim()}
                  isLoading={isSubmitting}
                >
                  <Send className="h-3 w-3 mr-1" />
                  Add
                </Button>
              </div>
            </div>
          </form>

          <div className="mt-3 pt-3 border-t border-secondary-200">
            <p className="text-xs text-secondary-500">
              💡 Tip: Use <kbd className="px-1 py-0.5 bg-secondary-100 rounded text-xs">Ctrl+Shift+A</kbd> to open Quick Add from anywhere
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickAdd;
