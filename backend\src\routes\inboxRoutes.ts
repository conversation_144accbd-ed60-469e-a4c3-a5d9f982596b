import { Router } from 'express';
import { InboxController } from '../controllers/inboxController';
import { protectRoute } from '../middlewares/authMiddleware';

const router = Router();

// All inbox routes require authentication
router.use(protectRoute);

// Inbox routes
router.post('/', InboxController.addInboxItem);
router.get('/', InboxController.getInboxItems);
router.post('/:id/process', InboxController.processInboxItem);
router.delete('/:id', InboxController.deleteInboxItem);

export default router;
