import { Router } from 'express';
import { AuthController } from '../controllers/authController';
import { protectRoute } from '../middlewares/authMiddleware';

const router = Router();

// Public routes
router.post('/register', AuthController.register);
router.post('/login', AuthController.login);
router.post('/logout', AuthController.logout);

// Protected routes
router.get('/me', protectRoute, AuthController.getMe);
router.post('/refresh', protectRoute, AuthController.refreshToken);

export default router;
