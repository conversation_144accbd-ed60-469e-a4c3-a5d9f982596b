import React from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import {
  Home,
  Inbox,
  FolderOpen,
  Plus,
  User,
  LogOut,
  Settings,
  Brain
} from 'lucide-react';
import { useAuthStore } from '@/store/authStore';
import { useProjectStore } from '@/store/projectStore';
import { cn } from '@/utils/cn';

const MainLayout: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuthStore();
  const { projects } = useProjectStore();

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const navigation = [
    {
      name: 'Today',
      href: '/dashboard',
      icon: Home,
      current: location.pathname === '/dashboard',
    },
    {
      name: 'Inbox',
      href: '/inbox',
      icon: Inbox,
      current: location.pathname === '/inbox',
    },
    {
      name: 'Projects',
      href: '/projects',
      icon: FolderOpen,
      current: location.pathname.startsWith('/projects'),
    },
  ];

  return (
    <div className="min-h-screen bg-secondary-50">
      {/* Sidebar */}
      <div className="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-soft-lg">
        <div className="flex h-full flex-col">
          {/* Logo */}
          <div className="flex h-16 items-center justify-center border-b border-secondary-200 px-6">
            <div className="flex items-center space-x-2">
              <Brain className="h-8 w-8 text-primary-600" />
              <span className="text-xl font-bold text-secondary-900">SimpleLife</span>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 space-y-1 px-4 py-6">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={cn(
                    'group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors focus-ring',
                    item.current
                      ? 'bg-primary-50 text-primary-700'
                      : 'text-secondary-700 hover:bg-secondary-100 hover:text-secondary-900'
                  )}
                >
                  <Icon
                    className={cn(
                      'mr-3 h-5 w-5 flex-shrink-0',
                      item.current
                        ? 'text-primary-500'
                        : 'text-secondary-400 group-hover:text-secondary-500'
                    )}
                  />
                  {item.name}
                </Link>
              );
            })}

            {/* Projects Section */}
            <div className="pt-6">
              <div className="flex items-center justify-between px-3 py-2">
                <h3 className="text-xs font-semibold uppercase tracking-wider text-secondary-500">
                  Projects
                </h3>
                <Link
                  to="/projects/new"
                  className="rounded-md p-1 text-secondary-400 hover:bg-secondary-100 hover:text-secondary-600 focus-ring"
                >
                  <Plus className="h-4 w-4" />
                </Link>
              </div>
              <div className="space-y-1">
                {projects.slice(0, 5).map((project) => (
                  <Link
                    key={project.id}
                    to={`/projects/${project.id}`}
                    className={cn(
                      'group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors focus-ring',
                      location.pathname === `/projects/${project.id}`
                        ? 'bg-primary-50 text-primary-700'
                        : 'text-secondary-700 hover:bg-secondary-100 hover:text-secondary-900'
                    )}
                  >
                    <div
                      className="mr-3 h-3 w-3 flex-shrink-0 rounded-full"
                      style={{ backgroundColor: project.color || '#64748b' }}
                    />
                    <span className="truncate">{project.name}</span>
                    {project._count && (
                      <span className="ml-auto text-xs text-secondary-500">
                        {project._count.tasks}
                      </span>
                    )}
                  </Link>
                ))}
                {projects.length > 5 && (
                  <Link
                    to="/projects"
                    className="group flex items-center rounded-lg px-3 py-2 text-sm font-medium text-secondary-500 hover:bg-secondary-100 hover:text-secondary-700 focus-ring"
                  >
                    <span className="text-xs">View all projects...</span>
                  </Link>
                )}
              </div>
            </div>
          </nav>

          {/* User Menu */}
          <div className="border-t border-secondary-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-100">
                  <User className="h-5 w-5 text-primary-600" />
                </div>
              </div>
              <div className="ml-3 flex-1">
                <p className="text-sm font-medium text-secondary-900">
                  {user?.name || user?.email}
                </p>
                <p className="text-xs text-secondary-500">
                  {user?.email}
                </p>
              </div>
              <div className="flex space-x-1">
                <button
                  type="button"
                  className="rounded-md p-1 text-secondary-400 hover:bg-secondary-100 hover:text-secondary-600 focus-ring"
                >
                  <Settings className="h-4 w-4" />
                </button>
                <button
                  type="button"
                  onClick={handleLogout}
                  className="rounded-md p-1 text-secondary-400 hover:bg-secondary-100 hover:text-secondary-600 focus-ring"
                >
                  <LogOut className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="pl-64">
        <main className="flex-1">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default MainLayout;
