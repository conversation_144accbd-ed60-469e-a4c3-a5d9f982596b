
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  passwordHash String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  projects   Project[]
  tasks      Task[]
  inboxItems InboxItem[]
}

model Project {
  id        String   @id @default(cuid())
  name      String
  color     String?  // Optional color for visual distinction
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  tasks Task[]

  @@index([userId])
}

model InboxItem {
  id          String        @id @default(cuid())
  content     String        // The raw captured thought
  status      InboxStatus   @default(UNPROCESSED)
  createdAt   DateTime      @default(now())
  processedAt DateTime?     // When the item was actioned

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, status])
}

model Task {
  id             String      @id @default(cuid())
  title          String
  content        String?
  status         TaskStatus  @default(TODO)
  priority       Priority?
  dueDate        DateTime?
  effortEstimate Int?        // e.g., in minutes, or story points
  energyLevel    EnergyLevel?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  projectId String? // Can be null if it's a standalone task (e.g. from Inbox directly)
  project   Project? @relation(fields: [projectId], references: [id], onDelete: SetNull) // SetNull allows task to remain if project is deleted, or make it Cascade

  // For subtasks
  parentId String?
  parent   Task?   @relation("SubTasks", fields: [parentId], references: [id], onDelete: Cascade)
  subTasks Task[]  @relation("SubTasks")

  @@index([userId, status])
  @@index([userId, dueDate])
  @@index([projectId])
  @@index([parentId])
}

enum TaskStatus {
  TODO
  IN_PROGRESS
  DONE
  ARCHIVED
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum EnergyLevel {
  LOW
  MEDIUM
  HIGH
}

enum InboxStatus {
  UNPROCESSED
  PROCESSED // e.g., converted to task, note, or deleted
  DEFERRED
}
