import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/authService';
import { registerSchema, loginSchema } from '../config/validation';

export class AuthController {
  /**
   * Register a new user
   */
  static async register(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // Validate input
      const { email, password, name } = registerSchema.parse(req.body);

      // Register user
      const user = await AuthService.registerUser(email, password, name);

      // Generate token
      const token = AuthService.generateToken({
        id: user.id,
        email: user.email,
        name: user.name || undefined,
      });

      // Set cookie (optional, for additional security)
      res.cookie('token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      res.status(201).json({
        status: 'success',
        message: 'User registered successfully',
        data: {
          user,
          token,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Login user
   */
  static async login(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // Validate input
      const { email, password } = loginSchema.parse(req.body);

      // Login user
      const user = await AuthService.loginUser(email, password);

      // Generate token
      const token = AuthService.generateToken({
        id: user.id,
        email: user.email,
        name: user.name || undefined,
      });

      // Set cookie (optional, for additional security)
      res.cookie('token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      res.json({
        status: 'success',
        message: 'Login successful',
        data: {
          user,
          token,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Logout user
   */
  static async logout(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // Clear cookie
      res.clearCookie('token');

      res.json({
        status: 'success',
        message: 'Logout successful',
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get current user profile
   */
  static async getMe(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      // Get full user data
      const user = await AuthService.getUserById(req.user.id);

      res.json({
        status: 'success',
        data: {
          user,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Refresh token
   */
  static async refreshToken(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      // Generate new token
      const token = AuthService.generateToken({
        id: req.user.id,
        email: req.user.email,
        name: req.user.name || undefined,
      });

      // Set new cookie
      res.cookie('token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      res.json({
        status: 'success',
        message: 'Token refreshed successfully',
        data: {
          token,
        },
      });
    } catch (error) {
      next(error);
    }
  }
}
