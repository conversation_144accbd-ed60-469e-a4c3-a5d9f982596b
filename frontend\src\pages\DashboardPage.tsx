import React, { useEffect } from 'react';
import { Calendar, Clock, AlertCircle, CheckCircle2 } from 'lucide-react';
import { useTaskStore } from '@/store/taskStore';
import { useInboxStore } from '@/store/inboxStore';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import TaskItem from '@/components/TaskItem';
import QuickAdd from '@/components/QuickAdd';
import { formatDate } from '@/utils/date';

const DashboardPage: React.FC = () => {
  const { todayTasks, fetchTodayTasks, isLoading } = useTaskStore();
  const { inboxItems, fetchInboxItems } = useInboxStore();

  useEffect(() => {
    fetchTodayTasks();
    fetchInboxItems();
  }, [fetchTodayTasks, fetchInboxItems]);

  const today = new Date();
  const overdueTasks = todayTasks.filter(task => 
    task.dueDate && new Date(task.dueDate) < today && task.status !== 'DONE'
  );
  const todayTasksFiltered = todayTasks.filter(task => 
    !task.dueDate || new Date(task.dueDate).toDateString() === today.toDateString()
  );
  const completedToday = todayTasks.filter(task => task.status === 'DONE');

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-secondary-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-32 bg-secondary-200 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">
            Good {getGreeting()}, focus on what matters today
          </h1>
          <p className="text-secondary-600 mt-1">
            {formatDate(today.toISOString(), 'EEEE, MMMM d, yyyy')}
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-primary-100 rounded-lg">
                <Calendar className="h-5 w-5 text-primary-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-secondary-900">
                  Today's Tasks
                </p>
                <p className="text-2xl font-bold text-primary-600">
                  {todayTasksFiltered.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-error-100 rounded-lg">
                <AlertCircle className="h-5 w-5 text-error-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-secondary-900">
                  Overdue
                </p>
                <p className="text-2xl font-bold text-error-600">
                  {overdueTasks.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-success-100 rounded-lg">
                <CheckCircle2 className="h-5 w-5 text-success-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-secondary-900">
                  Completed
                </p>
                <p className="text-2xl font-bold text-success-600">
                  {completedToday.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-warning-100 rounded-lg">
                <Clock className="h-5 w-5 text-warning-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-secondary-900">
                  Inbox Items
                </p>
                <p className="text-2xl font-bold text-warning-600">
                  {inboxItems.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Overdue Tasks */}
        {overdueTasks.length > 0 && (
          <div className="lg:col-span-3">
            <Card>
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <AlertCircle className="h-5 w-5 text-error-600" />
                  <h2 className="text-lg font-semibold text-error-900">
                    Overdue Tasks ({overdueTasks.length})
                  </h2>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {overdueTasks.map((task) => (
                    <TaskItem key={task.id} task={task} showProject />
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Today's Tasks */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <h2 className="text-lg font-semibold text-secondary-900">
                Today's Focus ({todayTasksFiltered.length})
              </h2>
            </CardHeader>
            <CardContent>
              {todayTasksFiltered.length === 0 ? (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-secondary-400 mx-auto mb-4" />
                  <p className="text-secondary-600">
                    No tasks scheduled for today. Great job staying on top of things!
                  </p>
                </div>
              ) : (
                <div className="space-y-2">
                  {todayTasksFiltered.map((task) => (
                    <TaskItem key={task.id} task={task} showProject />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="space-y-6">
          {/* Inbox Preview */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-secondary-900">
                Inbox ({inboxItems.length})
              </h3>
            </CardHeader>
            <CardContent>
              {inboxItems.length === 0 ? (
                <p className="text-secondary-600 text-sm">
                  Your inbox is empty. Use Quick Add to capture thoughts!
                </p>
              ) : (
                <div className="space-y-2">
                  {inboxItems.slice(0, 3).map((item) => (
                    <div
                      key={item.id}
                      className="p-3 bg-secondary-50 rounded-lg text-sm"
                    >
                      <p className="text-secondary-900 line-clamp-2">
                        {item.content}
                      </p>
                    </div>
                  ))}
                  {inboxItems.length > 3 && (
                    <p className="text-xs text-secondary-500 text-center">
                      +{inboxItems.length - 3} more items
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Completed Tasks */}
          {completedToday.length > 0 && (
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold text-success-900">
                  Completed Today ({completedToday.length})
                </h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {completedToday.slice(0, 5).map((task) => (
                    <div
                      key={task.id}
                      className="flex items-center space-x-2 text-sm"
                    >
                      <CheckCircle2 className="h-4 w-4 text-success-600 flex-shrink-0" />
                      <span className="text-secondary-700 line-through">
                        {task.title}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Quick Add FAB */}
      <QuickAdd />
    </div>
  );
};

function getGreeting(): string {
  const hour = new Date().getHours();
  if (hour < 12) return 'morning';
  if (hour < 17) return 'afternoon';
  return 'evening';
}

export default DashboardPage;
