# Backend Development Plan for SimpleLife (MVP)

**Technology Stack:** Node.js, Express, TypeScript, Prisma (ORM), PostgreSQL.

**Objective:** Build a secure and robust API to support the core features of SimpleLife.

**Steps (15):**

1.  **Project Setup & Initial Configuration:**
    *   Initialize Node.js project: `npm init -y`.
    *   Install core dependencies: `npm install express typescript ts-node @types/express @types/node --save-dev` and `npm install dotenv bcryptjs jsonwebtoken cookie-parser cors @types/bcryptjs @types/jsonwebtoken @types/cookie-parser @types/cors`.
    *   Setup `tsconfig.json` for TypeScript compilation.
    *   Create basic project structure: `src/` (with `server.ts`, `routes/`, `controllers/`, `middlewares/`, `services/`, `config/`, `prisma/`).

2.  **Prisma & Database Setup:**
    *   Install Prisma CLI: `npm install prisma --save-dev`.
    *   Initialize Prisma: `npx prisma init --datasource-provider postgresql`.
    *   Configure `prisma/schema.prisma` with initial models (User, Project, Task, InboxItem - refer to `DB.md`).
    *   Set up `.env` file with `DATABASE_URL`.
    *   Run initial migration: `npx prisma migrate dev --name init`.

3.  **User Model & Authentication Service:**
    *   Finalize `User` model in `schema.prisma` (id, email, passwordHash, name, etc.).
    *   Implement `authService.ts` with functions for:
        *   Hashing passwords (`bcryptjs`).
        *   Comparing passwords.
        *   Generating JWT tokens (`jsonwebtoken`).
        *   Verifying JWT tokens.

4.  **User Authentication Endpoints (Controllers & Routes):**
    *   Create `authController.ts`:
        *   `register(req, res)`: Validate input, hash password, save user via Prisma, generate JWT.
        *   `login(req, res)`: Validate input, find user, compare password, generate JWT.
        *   `logout(req, res)`: Clear JWT cookie (if using cookies).
        *   `getMe(req, res)`: Return current user data based on token.
    *   Create `authRoutes.ts` and mount in `server.ts`.

5.  **Authentication Middleware:**
    *   Create `authMiddleware.ts` (`protectRoute`).
    *   This middleware will verify the JWT from `Authorization` header or cookie.
    *   If valid, attach user object to `req.user`. If not, return 401 Unauthorized.

6.  **Project Model & CRUD Endpoints:**
    *   Finalize `Project` model in `schema.prisma` (name, userId, etc.).
    *   Create `projectController.ts` with CRUD operations:
        *   `createProject(req, res)` (associates with `req.user.id`)
        *   `getProjects(req, res)` (for current user)
        *   `getProjectById(req, res)`
        *   `updateProject(req, res)`
        *   `deleteProject(req, res)`
    *   Create `projectRoutes.ts`, protect with `authMiddleware`.

7.  **Task Model & CRUD Endpoints:**
    *   Finalize `Task` model in `schema.prisma` (title, content, status, priority, dueDate, projectId, userId, etc.).
    *   Create `taskController.ts` with CRUD operations:
        *   `createTask(req, res)` (associates with `req.user.id` and a project)
        *   `getTasksByProject(req, res)`
        *   `getTaskById(req, res)`
        *   `updateTask(req, res)` (including status changes like "complete")
        *   `deleteTask(req, res)`
    *   Create `taskRoutes.ts`, protect with `authMiddleware`.

8.  **InboxItem Model & Endpoints:**
    *   Finalize `InboxItem` model in `schema.prisma` (content, userId, status).
    *   Create `inboxController.ts`:
        *   `addInboxItem(req, res)` (for the Universal Quick Add)
        *   `getInboxItems(req, res)` (for current user, unprocessed items)
        *   `processInboxItem(req, res)` (e.g., convert to task, delete; this might involve creating a task and then deleting/marking the inbox item).
    *   Create `inboxRoutes.ts`, protect with `authMiddleware`.

9.  **"Today" View Endpoint:**
    *   In `taskController.ts` or a dedicated `dashboardController.ts`:
        *   `getTodayTasks(req, res)`: Fetch tasks for the current user where `dueDate` is today or overdue, and status is not 'completed'.

10. **Input Validation:**
    *   Install a validation library (e.g., `zod` or `express-validator`).
    *   Implement validation middleware or use in controllers for all incoming request bodies/params to ensure data integrity.

11. **Global Error Handling Middleware:**
    *   Create `errorHandler.ts`.
    *   This middleware will catch errors passed by `next(error)` from controllers/services.
    *   It should send standardized JSON error responses (e.g., `{ status: 'error', message: '...', statusCode: ... }`).
    *   Handle Prisma-specific errors gracefully.

12. **CORS Configuration:**
    *   Use the `cors` middleware.
    *   Configure it to allow requests from your frontend domain (e.g., `localhost:5173` during development, production domain later).

13. **Environment Variable Management:**
    *   Use `dotenv` to load environment variables from a `.env` file (e.g., `DATABASE_URL`, `JWT_SECRET`, `PORT`, `CLIENT_URL`).
    *   Ensure `.env` is in `.gitignore`.

14. **Basic Logging:**
    *   Implement a simple logging mechanism (e.g., using `console.log` structuredly, or a lightweight library like `pino` or `morgan` for HTTP requests) for debugging and monitoring.

15. **API Documentation (Basic):**
    *   Consider adding comments for JSDoc or TypeDoc that can later be used to generate API documentation.
    *   Alternatively, manually create a simple Postman collection or markdown file outlining endpoints, request/response formats for the MVP.