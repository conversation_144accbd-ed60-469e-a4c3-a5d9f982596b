import React, { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { FolderOpen, Plus } from 'lucide-react';
import { useProjectStore } from '@/store/projectStore';
import { useTaskStore } from '@/store/taskStore';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import TaskItem from '@/components/TaskItem';
import QuickAdd from '@/components/QuickAdd';

const ProjectsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { projects, currentProject, fetchProjects, fetchProject, isLoading } = useProjectStore();
  const { tasks, fetchTasks } = useTaskStore();

  useEffect(() => {
    if (id) {
      fetchProject(id);
      fetchTasks({ projectId: id, topLevel: true });
    } else {
      fetchProjects();
    }
  }, [id, fetchProject, fetchProjects, fetchTasks]);

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-secondary-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-32 bg-secondary-200 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Show specific project
  if (id && currentProject) {
    return (
      <div className="p-6 space-y-6">
        {/* Project Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div
              className="p-2 rounded-lg"
              style={{ backgroundColor: currentProject.color || '#64748b' }}
            >
              <FolderOpen className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-secondary-900">
                {currentProject.name}
              </h1>
              <p className="text-secondary-600">
                {tasks.length} task{tasks.length !== 1 ? 's' : ''}
              </p>
            </div>
          </div>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Task
          </Button>
        </div>

        {/* Tasks */}
        <Card>
          <CardHeader>
            <h2 className="text-lg font-semibold text-secondary-900">
              Tasks
            </h2>
          </CardHeader>
          <CardContent>
            {tasks.length === 0 ? (
              <div className="text-center py-8">
                <FolderOpen className="h-12 w-12 text-secondary-400 mx-auto mb-4" />
                <p className="text-secondary-600">
                  No tasks in this project yet. Create your first task to get started!
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                {tasks.map((task) => (
                  <TaskItem key={task.id} task={task} />
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <QuickAdd />
      </div>
    );
  }

  // Show all projects
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-primary-100 rounded-lg">
            <FolderOpen className="h-6 w-6 text-primary-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-secondary-900">
              Projects
            </h1>
            <p className="text-secondary-600">
              Organize your tasks into meaningful contexts
            </p>
          </div>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          New Project
        </Button>
      </div>

      {/* Projects Grid */}
      {projects.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <FolderOpen className="h-16 w-16 text-secondary-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-secondary-900 mb-2">
              No projects yet
            </h3>
            <p className="text-secondary-600 mb-6 max-w-md mx-auto">
              Create your first project to start organizing your tasks into meaningful contexts.
            </p>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create First Project
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {projects.map((project) => (
            <Card key={project.id} className="hover:shadow-soft-lg transition-shadow cursor-pointer">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div
                      className="w-4 h-4 rounded-full flex-shrink-0"
                      style={{ backgroundColor: project.color || '#64748b' }}
                    />
                    <h3 className="font-semibold text-secondary-900 truncate">
                      {project.name}
                    </h3>
                  </div>
                </div>
                
                <div className="flex items-center justify-between text-sm text-secondary-600">
                  <span>
                    {project._count?.tasks || 0} task{project._count?.tasks !== 1 ? 's' : ''}
                  </span>
                  <span className="text-xs">
                    Created {new Date(project.createdAt).toLocaleDateString()}
                  </span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      <QuickAdd />
    </div>
  );
};

export default ProjectsPage;
