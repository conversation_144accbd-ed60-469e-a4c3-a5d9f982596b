import axios, { AxiosInstance, AxiosResponse } from 'axios';

// Types
export interface User {
  id: string;
  email: string;
  name?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface Project {
  id: string;
  name: string;
  color?: string;
  createdAt: string;
  updatedAt: string;
  _count?: {
    tasks: number;
  };
}

export interface Task {
  id: string;
  title: string;
  content?: string;
  status: 'TODO' | 'IN_PROGRESS' | 'DONE' | 'ARCHIVED';
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  dueDate?: string;
  effortEstimate?: number;
  energyLevel?: 'LOW' | 'MEDIUM' | 'HIGH';
  createdAt: string;
  updatedAt: string;
  project?: {
    id: string;
    name: string;
    color?: string;
  };
  parent?: {
    id: string;
    title: string;
  };
  subTasks?: Task[];
  _count?: {
    subTasks: number;
  };
}

export interface InboxItem {
  id: string;
  content: string;
  status: 'UNPROCESSED' | 'PROCESSED' | 'DEFERRED';
  createdAt: string;
  processedAt?: string;
}

export interface ApiResponse<T> {
  status: 'success' | 'error';
  message?: string;
  data?: T;
  errors?: Array<{
    field: string;
    message: string;
  }>;
}

export interface PaginatedResponse<T> {
  status: 'success';
  data: {
    [key: string]: T[] | {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('simplelife_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          localStorage.removeItem('simplelife_token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth endpoints
  async register(email: string, password: string, name?: string): Promise<{ user: User; token: string }> {
    const response: AxiosResponse<ApiResponse<{ user: User; token: string }>> = await this.api.post('/auth/register', {
      email,
      password,
      name,
    });
    return response.data.data!;
  }

  async login(email: string, password: string): Promise<{ user: User; token: string }> {
    const response: AxiosResponse<ApiResponse<{ user: User; token: string }>> = await this.api.post('/auth/login', {
      email,
      password,
    });
    return response.data.data!;
  }

  async logout(): Promise<void> {
    await this.api.post('/auth/logout');
  }

  async getMe(): Promise<User> {
    const response: AxiosResponse<ApiResponse<{ user: User }>> = await this.api.get('/auth/me');
    return response.data.data!.user;
  }

  // Project endpoints
  async getProjects(): Promise<Project[]> {
    const response: AxiosResponse<PaginatedResponse<Project>> = await this.api.get('/projects');
    return response.data.data.projects;
  }

  async getProject(id: string): Promise<Project> {
    const response: AxiosResponse<ApiResponse<{ project: Project }>> = await this.api.get(`/projects/${id}`);
    return response.data.data!.project;
  }

  async createProject(data: { name: string; color?: string }): Promise<Project> {
    const response: AxiosResponse<ApiResponse<{ project: Project }>> = await this.api.post('/projects', data);
    return response.data.data!.project;
  }

  async updateProject(id: string, data: { name?: string; color?: string }): Promise<Project> {
    const response: AxiosResponse<ApiResponse<{ project: Project }>> = await this.api.put(`/projects/${id}`, data);
    return response.data.data!.project;
  }

  async deleteProject(id: string): Promise<void> {
    await this.api.delete(`/projects/${id}`);
  }

  // Task endpoints
  async getTasks(filters?: {
    projectId?: string;
    status?: string;
    priority?: string;
    parentId?: string;
    topLevel?: boolean;
  }): Promise<Task[]> {
    const params = new URLSearchParams();
    if (filters?.projectId) params.append('projectId', filters.projectId);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.priority) params.append('priority', filters.priority);
    if (filters?.parentId) params.append('parentId', filters.parentId);
    if (filters?.topLevel) params.append('topLevel', 'true');

    const response: AxiosResponse<PaginatedResponse<Task>> = await this.api.get(`/tasks?${params}`);
    return response.data.data.tasks;
  }

  async getTodayTasks(): Promise<Task[]> {
    const response: AxiosResponse<ApiResponse<{ tasks: Task[] }>> = await this.api.get('/tasks/today');
    return response.data.data!.tasks;
  }

  async getTask(id: string): Promise<Task> {
    const response: AxiosResponse<ApiResponse<{ task: Task }>> = await this.api.get(`/tasks/${id}`);
    return response.data.data!.task;
  }

  async createTask(data: {
    title: string;
    content?: string;
    priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
    dueDate?: string;
    effortEstimate?: number;
    energyLevel?: 'LOW' | 'MEDIUM' | 'HIGH';
    projectId?: string;
    parentId?: string;
  }): Promise<Task> {
    const response: AxiosResponse<ApiResponse<{ task: Task }>> = await this.api.post('/tasks', data);
    return response.data.data!.task;
  }

  async updateTask(id: string, data: {
    title?: string;
    content?: string;
    status?: 'TODO' | 'IN_PROGRESS' | 'DONE' | 'ARCHIVED';
    priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
    dueDate?: string;
    effortEstimate?: number;
    energyLevel?: 'LOW' | 'MEDIUM' | 'HIGH';
    projectId?: string;
    parentId?: string;
  }): Promise<Task> {
    const response: AxiosResponse<ApiResponse<{ task: Task }>> = await this.api.put(`/tasks/${id}`, data);
    return response.data.data!.task;
  }

  async deleteTask(id: string): Promise<void> {
    await this.api.delete(`/tasks/${id}`);
  }

  // Inbox endpoints
  async getInboxItems(): Promise<InboxItem[]> {
    const response: AxiosResponse<PaginatedResponse<InboxItem>> = await this.api.get('/inbox');
    return response.data.data.inboxItems;
  }

  async addInboxItem(content: string): Promise<InboxItem> {
    const response: AxiosResponse<ApiResponse<{ inboxItem: InboxItem }>> = await this.api.post('/inbox', { content });
    return response.data.data!.inboxItem;
  }

  async processInboxItem(id: string, action: 'convert_to_task' | 'delete' | 'defer', taskData?: {
    title: string;
    content?: string;
    priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
    dueDate?: string;
    effortEstimate?: number;
    energyLevel?: 'LOW' | 'MEDIUM' | 'HIGH';
    projectId?: string;
  }): Promise<{ task?: Task }> {
    const response: AxiosResponse<ApiResponse<{ task?: Task }>> = await this.api.post(`/inbox/${id}/process`, {
      action,
      taskData,
    });
    return response.data.data!;
  }

  async deleteInboxItem(id: string): Promise<void> {
    await this.api.delete(`/inbox/${id}`);
  }
}

export const apiService = new ApiService();
