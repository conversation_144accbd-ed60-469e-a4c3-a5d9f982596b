import { z } from 'zod';

// Auth validation schemas
export const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  name: z.string().min(1, 'Name is required').optional(),
});

export const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
});

// Project validation schemas
export const createProjectSchema = z.object({
  name: z.string().min(1, 'Project name is required').max(100, 'Project name too long'),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format').optional(),
});

export const updateProjectSchema = z.object({
  name: z.string().min(1, 'Project name is required').max(100, 'Project name too long').optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format').optional(),
});

// Task validation schemas
export const createTaskSchema = z.object({
  title: z.string().min(1, 'Task title is required').max(200, 'Task title too long'),
  content: z.string().max(2000, 'Task content too long').optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
  dueDate: z.string().datetime().optional(),
  effortEstimate: z.number().int().min(1).max(1440).optional(), // 1 minute to 24 hours
  energyLevel: z.enum(['LOW', 'MEDIUM', 'HIGH']).optional(),
  projectId: z.string().cuid().optional(),
  parentId: z.string().cuid().optional(),
});

export const updateTaskSchema = z.object({
  title: z.string().min(1, 'Task title is required').max(200, 'Task title too long').optional(),
  content: z.string().max(2000, 'Task content too long').optional(),
  status: z.enum(['TODO', 'IN_PROGRESS', 'DONE', 'ARCHIVED']).optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
  dueDate: z.string().datetime().optional(),
  effortEstimate: z.number().int().min(1).max(1440).optional(),
  energyLevel: z.enum(['LOW', 'MEDIUM', 'HIGH']).optional(),
  projectId: z.string().cuid().optional(),
  parentId: z.string().cuid().optional(),
});

// Inbox validation schemas
export const createInboxItemSchema = z.object({
  content: z.string().min(1, 'Content is required').max(1000, 'Content too long'),
});

export const processInboxItemSchema = z.object({
  action: z.enum(['convert_to_task', 'delete', 'defer']),
  taskData: z.object({
    title: z.string().min(1, 'Task title is required').max(200, 'Task title too long'),
    content: z.string().max(2000, 'Task content too long').optional(),
    priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
    dueDate: z.string().datetime().optional(),
    effortEstimate: z.number().int().min(1).max(1440).optional(),
    energyLevel: z.enum(['LOW', 'MEDIUM', 'HIGH']).optional(),
    projectId: z.string().cuid().optional(),
  }).optional(),
});

// Common validation schemas
export const idParamSchema = z.object({
  id: z.string().cuid('Invalid ID format'),
});

export const paginationSchema = z.object({
  page: z.string().regex(/^\d+$/).transform(Number).refine(n => n > 0).optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).refine(n => n > 0 && n <= 100).optional(),
});

// Type exports for TypeScript
export type RegisterInput = z.infer<typeof registerSchema>;
export type LoginInput = z.infer<typeof loginSchema>;
export type CreateProjectInput = z.infer<typeof createProjectSchema>;
export type UpdateProjectInput = z.infer<typeof updateProjectSchema>;
export type CreateTaskInput = z.infer<typeof createTaskSchema>;
export type UpdateTaskInput = z.infer<typeof updateTaskSchema>;
export type CreateInboxItemInput = z.infer<typeof createInboxItemSchema>;
export type ProcessInboxItemInput = z.infer<typeof processInboxItemSchema>;
export type IdParam = z.infer<typeof idParamSchema>;
export type PaginationQuery = z.infer<typeof paginationSchema>;
