import { Router } from 'express';
import { ProjectController } from '../controllers/projectController';
import { protectRoute } from '../middlewares/authMiddleware';

const router = Router();

// All project routes require authentication
router.use(protectRoute);

// Project routes
router.post('/', ProjectController.createProject);
router.get('/', ProjectController.getProjects);
router.get('/:id', ProjectController.getProjectById);
router.put('/:id', ProjectController.updateProject);
router.delete('/:id', ProjectController.deleteProject);

export default router;
