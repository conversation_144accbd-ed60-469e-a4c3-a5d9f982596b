
---

**`FULLPlan.md`**
```markdown
# Full Project Implementation Plan & Order of Operations: FocusFlow

This document outlines the integrated plan for building the FocusFlow application, coordinating Frontend, Backend, and Database development from start to MVP completion.

## Phase 0: Foundation & Setup (Sprint 0)

1.  **Project Initialization:**
    *   Create a monorepo structure (e.g., using `npm workspaces`, `pnpm workspaces`, or just separate folders `frontend/` and `backend/` under one Git repository).
    *   Initialize Git repository: `git init`.
    *   Create initial `README.md` (as defined).
2.  **Technology Stack Confirmation:**
    *   Frontend: React, Vite, TypeScript, Tailwind CSS, Zustand, React Router.
    *   Backend: Node.js, Express, TypeScript, Prisma ORM.
    *   Database: PostgreSQL.
3.  **Environment Setup:**
    *   Ensure development tools are installed (Node.js, npm/pnpm, Docker for PostgreSQL if preferred, IDEs).
    *   Basic project structure for both frontend and backend (as per their respective plans).

## Phase 1: Backend Core - User Authentication & Initial Schema (Sprint 1)

*Goal: Users can register and log in securely.*

1.  **(Backend Step 1, 2):** Setup Node.js/Express/TypeScript backend project. Install Prisma.
2.  **(DB.md):** Define initial `User` model in `prisma/schema.prisma`. Configure `DATABASE_URL` in `.env`.
3.  **(DB.md / Backend Step 2):** Run `npx prisma migrate dev --name initial-user-schema` to create the `User` table.
4.  **(Backend Step 3, 4):** Implement User Authentication service (password hashing, JWT generation) and API endpoints (`/auth/register`, `/auth/login`, `/auth/me`).
5.  **(Backend Step 5):** Implement `authMiddleware` to protect routes.
6.  **Testing:** Use Postman or a similar tool to test authentication endpoints.

## Phase 2: Frontend Core - User Authentication UI & Basic Layout (Sprint 2)

*Goal: Users can interact with login/signup forms and access a basic dashboard shell.*

1.  **(Frontend Step 1, 2):** Setup React/Vite/TypeScript frontend project. Integrate Tailwind CSS.
2.  **(Frontend Step 3):** Create `MainLayout.tsx` and `AuthLayout.tsx`.
3.  **(Frontend Step 4):** Setup basic routing (`/login`, `/signup`, `/dashboard`).
4.  **(Frontend Step 5):** Build UI for Login and Signup pages/forms.
5.  **(Frontend Step 6):** Setup `userStore` with Zustand for authentication state.
6.  **(Frontend Step 7):** Create API service module for frontend to communicate with backend.
7.  **(Frontend Step 8):** Integrate Frontend Auth UI with Backend Auth endpoints. Implement protected routes.
8.  **Testing:** Test full registration and login flow from the UI.

## Phase 3: Core Feature - Quick Add & Inbox (Sprint 3)

*Goal: Users can capture thoughts via Quick Add and view them in an Inbox.*

1.  **(DB.md / Backend Step 2):** Define `InboxItem` model in `prisma/schema.prisma`. Run migration.
2.  **(Backend Step 8):** Implement Backend CRUD endpoints for `InboxItem` (`/inbox`).
3.  **(Frontend Step 6):** Create `inboxStore` in Zustand.
4.  **(Frontend Step 9):** Develop the "Universal Quick Add" UI component.
5.  **(Frontend Step 10):** Develop the "Inbox" page/view UI.
6.  **Integration:** Connect Frontend Quick Add and Inbox view to Backend Inbox endpoints.
7.  **Testing:** Test adding items via Quick Add and seeing them in the Inbox. Test basic processing actions (placeholders for now, e.g., delete).

## Phase 4: Core Feature - Projects & Tasks (Sprint 4-5)

*Goal: Users can create projects, add tasks to them, and view tasks in a "Today" list.*

1.  **(DB.md / Backend Step 2):** Define `Project` and `Task` models in `prisma/schema.prisma`. Define enums. Run migration.
2.  **(Backend Step 6):** Implement Backend CRUD endpoints for `Project` (`/projects`).
3.  **(Backend Step 7):** Implement Backend CRUD endpoints for `Task` (`/tasks`).
4.  **(Backend Step 9):** Implement Backend endpoint for "Today" view (`/tasks/today`).
5.  **(Frontend Step 6):** Create `projectStore` and `taskStore` in Zustand.
6.  **(Frontend Step 11):** Develop basic Project management UI (create, list).
7.  **(Frontend Step 12):** Develop `TaskItem.tsx` component and "Today" page/view.
8.  **(Frontend Step 13):** Develop basic Task creation/viewing UI (form/modal).
9.  **Integration:**
    *   Connect Project UI to Backend Project endpoints.
    *   Connect Task creation/viewing UI to Backend Task endpoints.
    *   Connect "Today" view to fetch data from `/tasks/today`.
    *   Integrate "Convert to Task" from Inbox to create a task.
10. **Testing:** Test creating projects, adding tasks, viewing tasks in project lists and "Today" view, marking tasks complete.

## Phase 5: Refinement & MVP Polish (Sprint 6)

*Goal: Ensure MVP is stable, usable, and meets core requirements.*

1.  **(Backend Step 10):** Implement robust input validation on all backend endpoints.
2.  **(Backend Step 11):** Implement global error handling middleware on the backend.
3.  **(Backend Step 12):** Configure CORS properly.
4.  **(Backend Step 13, 14):** Ensure proper environment variable management and basic logging.
5.  **(Frontend Step 14):** Finalize styling, theming, and ensure basic responsiveness across devices.
6.  **(Frontend Step 15):** Implement consistent UI for loading states and error messages.
7.  **End-to-End Testing:** Conduct thorough testing of all MVP features and user flows.
8.  **(Backend Step 15):** Create basic API documentation (e.g., Postman collection).
9.  **Build & Deployment Preparation:**
    *   Create build scripts for frontend and backend.
    *   Research and decide on hosting platforms (e.g., Frontend: Vercel/Netlify; Backend: Render/Fly.io; DB: Supabase/Neon/Railway).

## Deployment Strategy (Brief Overview)

*   **Database:** Deploy PostgreSQL instance first (e.g., on Supabase, Neon, Railway, AWS RDS). Update `DATABASE_URL` in backend environment variables.
*   **Backend:** Containerize (Docker optional but recommended) and deploy to a platform like Render, Fly.io, or Heroku. Configure environment variables. Run `prisma migrate deploy`.
*   **Frontend:** Build static assets and deploy to a platform like Vercel or Netlify. Configure environment variables for API base URL.
*   **CI/CD:** (Post-MVP) Set up basic CI/CD pipelines (e.g., GitHub Actions) to automate testing, building, and deployment.

## Development Workflow & Practices

*   **Version Control:** Use Git diligently. Create feature branches for each significant piece of work. Use Pull Requests for code review before merging to `main`/`develop`.
*   **Issue Tracking:** Use GitHub Issues (or similar) to track tasks, bugs, and features.
*   **Regular Communication:** If working in a team, maintain regular communication (stand-ups, async updates).
*   **Code Reviews:** Enforce code reviews to maintain quality and share knowledge.
*   **Testing:** Write unit/integration tests as feasible (especially for backend logic). Manual E2E testing for UI flows.
*   **Iterative Development:** Build in small, manageable chunks. Get feedback early and often if possible.

This `FULLPlan.md` provides a high-level sequence. Individual steps within `FrontendPlan.md` and `BackendPlan.md` will be executed within these phases. Adjust sprint duration and task allocation based on resources and complexity.