import React, { useEffect, useState } from 'react';
import { 
  Inbox, 
  ArrowRight, 
  Trash2, 
  Clock, 
  Plus,
  CheckCircle2
} from 'lucide-react';
import { useInboxStore } from '@/store/inboxStore';
import { useProjectStore } from '@/store/projectStore';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Modal from '@/components/ui/Modal';
import QuickAdd from '@/components/QuickAdd';
import ConvertToTaskModal from '@/components/ConvertToTaskModal';
import { formatDate, getRelativeTime } from '@/utils/date';
import { InboxItem } from '@/services/api';

const InboxPage: React.FC = () => {
  const { inboxItems, fetchInboxItems, processInboxItem, deleteInboxItem, isLoading } = useInboxStore();
  const { projects, fetchProjects } = useProjectStore();
  const [selectedItem, setSelectedItem] = useState<InboxItem | null>(null);
  const [showConvertModal, setShowConvertModal] = useState(false);

  useEffect(() => {
    fetchInboxItems();
    fetchProjects();
  }, [fetchInboxItems, fetchProjects]);

  const handleConvertToTask = (item: InboxItem) => {
    setSelectedItem(item);
    setShowConvertModal(true);
  };

  const handleDelete = async (item: InboxItem) => {
    if (window.confirm('Are you sure you want to delete this item?')) {
      try {
        await deleteInboxItem(item.id);
      } catch (error) {
        console.error('Failed to delete inbox item:', error);
      }
    }
  };

  const handleDefer = async (item: InboxItem) => {
    try {
      await processInboxItem(item.id, 'defer');
    } catch (error) {
      console.error('Failed to defer inbox item:', error);
    }
  };

  const handleConvertSuccess = () => {
    setShowConvertModal(false);
    setSelectedItem(null);
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-secondary-200 rounded w-1/3"></div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-24 bg-secondary-200 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-warning-100 rounded-lg">
            <Inbox className="h-6 w-6 text-warning-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-secondary-900">
              Inbox
            </h1>
            <p className="text-secondary-600">
              Process your captured thoughts and ideas
            </p>
          </div>
        </div>
        <div className="text-right">
          <p className="text-2xl font-bold text-warning-600">
            {inboxItems.length}
          </p>
          <p className="text-sm text-secondary-600">
            items to process
          </p>
        </div>
      </div>

      {/* Instructions */}
      {inboxItems.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Inbox className="h-16 w-16 text-secondary-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-secondary-900 mb-2">
              Your inbox is empty!
            </h3>
            <p className="text-secondary-600 mb-6 max-w-md mx-auto">
              Use the Quick Add button to capture thoughts, ideas, and tasks as they come to mind. 
              Then come back here to organize them properly.
            </p>
            <div className="flex justify-center space-x-4">
              <Button onClick={() => {}}>
                <Plus className="h-4 w-4 mr-2" />
                Add First Item
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Inbox Items */}
      {inboxItems.length > 0 && (
        <div className="space-y-4">
          {inboxItems.map((item) => (
            <Card key={item.id} className="hover:shadow-soft-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <p className="text-secondary-900 mb-2 leading-relaxed">
                      {item.content}
                    </p>
                    <div className="flex items-center space-x-4 text-xs text-secondary-500">
                      <span>Added {getRelativeTime(item.createdAt)}</span>
                      <span>•</span>
                      <span>{formatDate(item.createdAt, 'MMM d, h:mm a')}</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleConvertToTask(item)}
                      className="text-primary-600 hover:text-primary-700"
                    >
                      <ArrowRight className="h-4 w-4 mr-1" />
                      Convert to Task
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDefer(item)}
                      className="text-warning-600 hover:text-warning-700"
                    >
                      <Clock className="h-4 w-4 mr-1" />
                      Defer
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(item)}
                      className="text-error-600 hover:text-error-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Processing Tips */}
      {inboxItems.length > 0 && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-secondary-900">
              💡 Processing Tips
            </h3>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="flex items-start space-x-2">
                <ArrowRight className="h-4 w-4 text-primary-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-secondary-900">Convert to Task</p>
                  <p className="text-secondary-600">Turn actionable items into organized tasks</p>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <Clock className="h-4 w-4 text-warning-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-secondary-900">Defer</p>
                  <p className="text-secondary-600">Save for later review if not ready to act</p>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <Trash2 className="h-4 w-4 text-error-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-secondary-900">Delete</p>
                  <p className="text-secondary-600">Remove items that are no longer relevant</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Convert to Task Modal */}
      {selectedItem && (
        <ConvertToTaskModal
          isOpen={showConvertModal}
          onClose={() => {
            setShowConvertModal(false);
            setSelectedItem(null);
          }}
          inboxItem={selectedItem}
          projects={projects}
          onSuccess={handleConvertSuccess}
        />
      )}

      {/* Quick Add FAB */}
      <QuickAdd />
    </div>
  );
};

export default InboxPage;
