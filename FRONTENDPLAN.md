
---

**`FrontendPlan.md`**
```markdown
# Frontend Development Plan for FocusFlow (MVP)

**Technology Stack:** React (with Vite), TypeScript, Tailwind CSS, Zustand (for state management), React Router DOM.

**Objective:** Build a responsive, user-friendly interface for the core features of FocusFlow.

**Steps (15):**

1.  **Project Setup & Initial Configuration:**
    *   Initialize a new React project using Vite: `npm create vite@latest frontend -- --template react-ts`.
    *   Install essential dependencies: `npm install react-router-dom zustand axios lucide-react` (Lucide for icons).
    *   Set up basic project structure (folders for `components`, `pages`, `services`, `store`, `assets`, `layouts`).

2.  **Tailwind CSS Integration:**
    *   Install and configure Tailwind CSS as per its official documentation for Vite.
    *   Create a `tailwind.config.js` and `postcss.config.js`.
    *   Import Tailwind directives into `src/index.css`.
    *   Define a basic theme (colors, fonts) in `tailwind.config.js` aligned with the "calm & clean" design philosophy.

3.  **Core Layout Component(s):**
    *   Create a `MainLayout.tsx` component: This will include a persistent navigation bar (if any for MVP, perhaps a simple header) and a main content area where different pages will render.
    *   Potentially a `AuthLayout.tsx` for login/signup pages if they have a different structure.

4.  **Routing Setup:**
    *   Configure `react-router-dom` in `App.tsx` or a dedicated `Router.tsx` file.
    *   Define initial routes: `/login`, `/signup`, `/dashboard` (Today view), `/inbox`, `/projects/:projectId`.

5.  **User Authentication Pages & Forms:**
    *   Create `LoginPage.tsx` and `SignupPage.tsx`.
    *   Build reusable form components (e.g., `Input.tsx`, `Button.tsx`) styled with Tailwind CSS.
    *   Implement forms for login (email, password) and signup (name, email, password). (UI only, no API calls yet).

6.  **State Management (Zustand):**
    *   Create a `userStore.ts` to manage user authentication state (e.g., `user`, `token`, `isAuthenticated`, `login()`, `logout()`, `register()` actions).
    *   Create an `inboxStore.ts` to manage items in the quick-add inbox.
    *   Create a `taskStore.ts` and `projectStore.ts` for managing tasks and projects.

7.  **API Service Module:**
    *   Create `services/api.ts` (or similar).
    *   Set up an Axios instance with base URL and interceptors for adding auth tokens to requests and handling global API errors.
    *   Define functions for authentication API calls (`loginUser`, `registerUser`).

8.  **Integrate Authentication Flow:**
    *   Connect Login/Signup forms to call API service functions.
    *   On successful login/signup, update `userStore` and redirect to the dashboard.
    *   Implement protected routes that redirect to `/login` if the user is not authenticated.

9.  **"Universal Quick Add" Component:**
    *   Create a `QuickAddComponent.tsx`.
    *   This could be a floating action button (FAB) that opens a small modal/dialog with a text input.
    *   On submit, it will (eventually) call an API to add the item to the backend Inbox. For now, can add to `inboxStore`.

10. **"Inbox" Page/View:**
    *   Create `InboxPage.tsx`.
    *   Display items from the `inboxStore` (later fetched from API).
    *   For each item, provide UI elements (buttons/icons) for actions: "Convert to Task," "Delete." (Functionality to be fully implemented later).

11. **"Project" Management UI (Basic):**
    *   Create a `ProjectsSidebar.tsx` or section to list projects.
    *   UI for creating a new project (e.g., a modal with a name input).
    *   Display a list of projects (fetched from API, managed in `projectStore`).
    *   Clicking a project should navigate to a project-specific view (or filter tasks).

12. **"Task" Item Component & "Today" View:**
    *   Create `TaskItem.tsx` to display a single task (title, due date, priority indicator, checkbox).
    *   Create `TodayPage.tsx` (or `DashboardPage.tsx`).
    *   Fetch and display tasks due "today" or overdue.
    *   Implement UI for marking tasks as complete.

13. **Basic Task Creation/Viewing UI:**
    *   When "Convert to Task" is clicked in Inbox, or a "New Task" button is clicked within a project:
        *   Show a modal/form (`TaskForm.tsx`) to input task details (title, description, project, due date, priority).
    *   Basic view/modal to see task details.

14. **Styling, Theming & Responsiveness:**
    *   Apply consistent styling using Tailwind utility classes and custom theme settings.
    *   Ensure basic responsiveness for common screen sizes (mobile, tablet, desktop).
    *   Use `lucide-react` icons appropriately for clarity.

15. **Basic Error Handling & Loading States:**
    *   Implement UI indicators for loading states (e.g., spinners) when fetching data.
    *   Display user-friendly error messages for API errors or form validation issues.
    *   Create a simple global notification/toast system for feedback.