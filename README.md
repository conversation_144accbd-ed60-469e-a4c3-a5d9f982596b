# FocusFlow: The ADHD-Friendly Task Management Web App

## Project Overview

FocusFlow is a task management web application meticulously designed to support users, especially those with ADHD or similar executive function challenges. It aims to move beyond traditional task managers like Trello by incorporating features and UI/UX principles specifically tailored to reduce overwhelm, aid focus, capture fleeting thoughts, and provide clear, actionable organization. The core philosophy is "Clarity First, Low Friction Capture, Guided Organization."

## Problem Statement

Many individuals, particularly those with ADHD, struggle with:
1.  **Task Initiation & Prioritization:** Difficulty starting tasks or switching between them inappropriately.
2.  **Working Memory & Idea Capture:** Random, important thoughts arise and are quickly forgotten if not captured immediately.
3.  **Organization & Overwhelm:** Traditional systems can become cluttered and hard to navigate, leading to avoidance.

FocusFlow addresses these by providing an intuitive system to easily capture, process, organize, and act on tasks and ideas, minimizing cognitive load and maximizing user-friendliness.

## Key Features (MVP & Core Vision)

*   **Universal Quick Add ("Brain Dump"):** Instantly capture any thought without breaking flow.
*   **Dedicated Inbox:** A processing station to clarify and assign captured thoughts.
*   **Project-Based Organization:** Group tasks into meaningful contexts.
*   **"Today" / Focus View:** A clear, prioritized list of what to work on *now*.
*   **Multiple Task Views (Planned):** List, Kanban, Calendar for different perspectives.
*   **ADHD-Friendly UI/UX:** Calm design, minimal clutter, clear actions, progressive disclosure.
*   **Reminders & Notifications:** To keep users on track.
*   **Visual Progress & Positive Reinforcement:** To motivate and encourage.

## Target Audience

*   Individuals with ADHD or executive function challenges.
*   Students, professionals, freelancers, and anyone feeling overwhelmed by tasks and ideas.
*   Users seeking a more intuitive, less cluttered alternative to existing task managers.
*   The "average American" consumer who values user-friendly design and efficiency.

## Technology Stack (Planned)

*   **Frontend:** React (with Vite), TypeScript, Tailwind CSS, Zustand (or Context API)
*   **Backend:** Node.js, Express, TypeScript, Prisma ORM
*   **Database:** PostgreSQL
*   **Authentication:** JWT (JSON Web Tokens)

## Project Status

*   [ ] Planning & Design (Current)
*   [ ] MVP Development
*   [ ] Alpha/Beta Testing
*   [ ] Release
*   [ ] Post-Release Iteration

## Getting Started (For Developers - Placeholder)

```bash
# Clone the repository
git clone <repository-url>
cd focusflow

# Frontend Setup
cd frontend
npm install
npm run dev

# Backend Setup
cd backend
npm install
# (Setup .env file with database credentials)
npx prisma migrate dev
npm run dev
```

## Vision for the Future
FocusFlow aims to be more than just a to-do list. It strives to be a reliable "second brain" that actively helps users manage their lives with less stress and more clarity. See POSTPlan.md for a detailed roadmap of future enhancements.

## Contact & Contributions
*	Project Lead: [Your Name/Alias]
*	For Investors/Collaboration: [Your Contact Email]
*	Contributions are welcome once the foundational structure is in place. Please refer to CONTRIBUTING.md (to be created).