import React from 'react';
import { Brain } from 'lucide-react';

const LoadingSpinner: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100 flex items-center justify-center">
      <div className="text-center">
        <div className="flex justify-center mb-4">
          <Brain className="h-12 w-12 text-primary-600 animate-pulse-soft" />
        </div>
        <h2 className="text-xl font-semibold text-secondary-900 mb-2">
          SimpleLife
        </h2>
        <div className="flex items-center justify-center space-x-1">
          <div className="w-2 h-2 bg-primary-600 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-primary-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-primary-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
        <p className="text-sm text-secondary-600 mt-2">
          Loading your workspace...
        </p>
      </div>
    </div>
  );
};

export default LoadingSpinner;
