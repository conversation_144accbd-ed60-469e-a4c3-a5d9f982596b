# FocusFlow Database Schema & Implementation Plan

**Technology:** PostgreSQL
**ORM:** Prisma

## Introduction

This document outlines the database schema for the FocusFlow application. The schema is designed to support core task management functionalities, user accounts, and features catering to ADHD-friendly interactions. Prisma will be used as the Object-Relational Mapper (ORM) to interact with the PostgreSQL database.

## Schema Definition (`prisma/schema.prisma`)

'''prisma
// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  passwordHash String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  projects   Project[]
  tasks      Task[]
  inboxItems InboxItem[]
}

model Project {
  id        String   @id @default(cuid())
  name      String
  color     String?  // Optional color for visual distinction
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  tasks Task[]

  @@index([userId])
}

model InboxItem {
  id          String        @id @default(cuid())
  content     String        // The raw captured thought
  status      InboxStatus   @default(UNPROCESSED)
  createdAt   DateTime      @default(now())
  processedAt DateTime?     // When the item was actioned

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, status])
}

model Task {
  id             String      @id @default(cuid())
  title          String
  content        String?
  status         TaskStatus  @default(TODO)
  priority       Priority?
  dueDate        DateTime?
  effortEstimate Int?        // e.g., in minutes, or story points
  energyLevel    EnergyLevel?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  projectId String? // Can be null if it's a standalone task (e.g. from Inbox directly)
  project   Project? @relation(fields: [projectId], references: [id], onDelete: SetNull) // SetNull allows task to remain if project is deleted, or make it Cascade

  // For subtasks
  parentId String?
  parent   Task?   @relation("SubTasks", fields: [parentId], references: [id], onDelete: Cascade)
  subTasks Task[]  @relation("SubTasks")

  @@index([userId, status])
  @@index([userId, dueDate])
  @@index([projectId])
  @@index([parentId])
}

enum TaskStatus {
  TODO
  IN_PROGRESS
  DONE
  ARCHIVED
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum EnergyLevel {
  LOW
  MEDIUM
  HIGH
}

enum InboxStatus {
  UNPROCESSED
  PROCESSED // e.g., converted to task, note, or deleted
  DEFERRED
}'''

## Models Explanation:

*   **`User`**: Stores user account information.
    *   `passwordHash`: Stores the bcrypt-hashed password.
*   **`Project`**: Represents a collection of tasks, or a context.
    *   `userId`: Foreign key linking to the `User` who owns the project.
    *   `color`: Optional, for visual tagging.
*   **`InboxItem`**: Stores items captured via the "Universal Quick Add".
    *   `content`: The raw text of the thought.
    *   `status`: Tracks if the item is `UNPROCESSED`, `PROCESSED`, or `DEFERRED`.
    *   `userId`: Foreign key linking to the `User`.
*   **`Task`**: Represents an actionable to-do item.
    *   `status`: Tracks the task's progress (e.g., `TODO`, `IN_PROGRESS`, `DONE`).
    *   `priority`, `dueDate`, `effortEstimate`, `energyLevel`: Attributes for sorting, filtering, and planning.
    *   `userId`: Foreign key linking to the `User`.
    *   `projectId`: Optional foreign key linking to a `Project`.
    *   `parentId`, `subTasks`: Self-referencing relation for implementing sub-tasks.

## Enums:

*   **`TaskStatus`**: Defines possible states for a task.
*   **`Priority`**: Defines task priority levels.
*   **`EnergyLevel`**: Helps users match tasks to their current mental capacity.
*   **`InboxStatus`**: Defines states for items in the inbox.

## Relationships:

*   One-to-Many: `User` to `Project`, `User` to `Task`, `User` to `InboxItem`.
*   One-to-Many: `Project` to `Task`.
*   One-to-Many (Self-referential): `Task` to `Task` (for sub-tasks).
*   `onDelete: Cascade` is used where deleting a parent record should delete related child records (e.g., deleting a User deletes their Projects and Tasks). `onDelete: SetNull` for `Task.projectId` means if a project is deleted, associated tasks become unassigned rather than deleted (this can be debated based on desired behavior).

## Indexes:

*   Primary keys (`@id`) are automatically indexed.
*   Foreign keys are often automatically indexed by Prisma/PostgreSQL, but explicit `@index` or `@@index` are good practice for frequently queried fields.
*   `@@index([userId, status])` on `Task` and `InboxItem` for efficient querying by user and status.
*   `@@index([userId, dueDate])` on `Task` for efficient querying for "Today" view or date-based views.

## Migration Strategy:

*   Prisma Migrate will be used to manage schema changes and database migrations.
*   Development: `npx prisma migrate dev --name <migration-name>`
*   Production: `npx prisma migrate deploy`
*   Schema changes will be committed to version control along with the code.

## Implementation Notes:

*   The schema is designed for MVP. Future enhancements (see `POSTPlan.md`) might require adding new tables (e.g., `Tags`, `Comments`, `Attachments`) or modifying existing ones.
*   Careful consideration of `onDelete` behavior is important for data integrity. The current setup is a common starting point.
```
