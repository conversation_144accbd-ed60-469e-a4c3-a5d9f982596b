import { create } from 'zustand';
import { apiService, Project } from '@/services/api';

interface ProjectState {
  projects: Project[];
  currentProject: Project | null;
  isLoading: boolean;
  error: string | null;
}

interface ProjectActions {
  fetchProjects: () => Promise<void>;
  fetchProject: (id: string) => Promise<void>;
  createProject: (data: { name: string; color?: string }) => Promise<Project>;
  updateProject: (id: string, data: { name?: string; color?: string }) => Promise<Project>;
  deleteProject: (id: string) => Promise<void>;
  setCurrentProject: (project: Project | null) => void;
  clearError: () => void;
}

type ProjectStore = ProjectState & ProjectActions;

export const useProjectStore = create<ProjectStore>((set) => ({
  // Initial state
  projects: [],
  currentProject: null,
  isLoading: false,
  error: null,

  // Actions
  fetchProjects: async () => {
    try {
      set({ isLoading: true, error: null });

      const projects = await apiService.getProjects();

      set({
        projects,
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch projects';
      set({
        error: errorMessage,
        isLoading: false,
      });
      throw error;
    }
  },

  fetchProject: async (id: string) => {
    try {
      set({ isLoading: true, error: null });

      const project = await apiService.getProject(id);

      set({
        currentProject: project,
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch project';
      set({
        error: errorMessage,
        isLoading: false,
        currentProject: null,
      });
      throw error;
    }
  },

  createProject: async (data: { name: string; color?: string }) => {
    try {
      set({ isLoading: true, error: null });

      const newProject = await apiService.createProject(data);

      set((state) => ({
        projects: [newProject, ...state.projects],
        isLoading: false,
        error: null,
      }));

      return newProject;
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to create project';
      set({
        error: errorMessage,
        isLoading: false,
      });
      throw error;
    }
  },

  updateProject: async (id: string, data: { name?: string; color?: string }) => {
    try {
      set({ isLoading: true, error: null });

      const updatedProject = await apiService.updateProject(id, data);

      set((state) => ({
        projects: state.projects.map((project) =>
          project.id === id ? updatedProject : project
        ),
        currentProject: state.currentProject?.id === id ? updatedProject : state.currentProject,
        isLoading: false,
        error: null,
      }));

      return updatedProject;
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to update project';
      set({
        error: errorMessage,
        isLoading: false,
      });
      throw error;
    }
  },

  deleteProject: async (id: string) => {
    try {
      set({ isLoading: true, error: null });

      await apiService.deleteProject(id);

      set((state) => ({
        projects: state.projects.filter((project) => project.id !== id),
        currentProject: state.currentProject?.id === id ? null : state.currentProject,
        isLoading: false,
        error: null,
      }));
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to delete project';
      set({
        error: errorMessage,
        isLoading: false,
      });
      throw error;
    }
  },

  setCurrentProject: (project: Project | null) => {
    set({ currentProject: project });
  },

  clearError: () => {
    set({ error: null });
  },
}));
