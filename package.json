{"name": "focusflow", "version": "1.0.0", "description": "ADHD-friendly task management web application", "main": "index.js", "scripts": {"dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "build": "npm run build:backend && npm run build:frontend", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "install:all": "npm install && npm run install:backend && npm run install:frontend", "setup": "npm run install:all"}, "workspaces": ["frontend", "backend"], "keywords": ["task-management", "adhd", "productivity", "react", "nodejs", "typescript"], "author": "FocusFlow Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}